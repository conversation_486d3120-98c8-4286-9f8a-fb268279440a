<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盖大楼小游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let blocks = []; // 存储所有方块
    let currentBlock; // 当前正在移动的方块
    let monster; // 右边的怪物
    let gameStarted = false;
    let score = 0;
    let gameOver = false;
    let battleMode = false; // 战斗模式
    let startBattleButton; // 开始战斗按钮
    let ground; // 地基

    // 方块参数
    const BLOCK_SIZE = 100; // 方块大小 100x100
    const GROUND_WIDTH = 100; // 地基宽度
    const GROUND_HEIGHT = 30; // 地基高度
    const GROUND_Y = 1200; // 地基Y位置
    let blockSpeed = 0.001; // 方块移动速度（降低速度）
    let isDropping = false; // 方块是否正在下落
    






    // 游戏变量



    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#87CEEB', // 天蓝色背景
        physics: {
            default: 'matter',
            matter: {
                gravity: { y: 0.5 }, // 重力，调小一些使楼层下落更慢
                debug: true // 开启调试模式
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 不加载任何图片资源
    }







    // 创建游戏场景
    function create() {
        // 创建简单的背景色
        const background = this.add.graphics();
        background.fillStyle(0x87CEEB); // 天蓝色背景
        background.fillRect(0, 0, 750, 1334); // 填充整个游戏区域
        background.setDepth(-10); // 设置为最底层

        // 初始化游戏变量
        blocks = [];
        score = 0;
        gameOver = false;
        gameStarted = true;
        battleMode = false;

        // 创建游戏边界
        createGameBounds.call(this);

        // 创建地基
        createGround.call(this);

        // 创建第一个方块
        createNewBlock.call(this);

        // 创建UI
        createUI.call(this);

        // 设置输入事件
        setupInput.call(this);
    }

    function update() {
        if (gameStarted && !gameOver) {
            // 更新当前方块的移动
            if (currentBlock && currentBlock.active && !isDropping) {
                // 获取物理对象的位置
                const currentBlockX = currentBlock.position ? currentBlock.position.x : currentBlock.x;
                const currentBlockY = currentBlock.position ? currentBlock.position.y : currentBlock.y;

                // 计算新位置
                let newX = currentBlockX + blockSpeed;

                // 如果方块碰到右边界，改变方向
                if (newX >= 750 - BLOCK_SIZE/2) {
                    newX = 750 - BLOCK_SIZE/2;
                    blockSpeed = -Math.abs(blockSpeed);
                }

                // 如果方块碰到左边界，改变方向
                if (newX <= BLOCK_SIZE/2) {
                    newX = BLOCK_SIZE/2;
                    blockSpeed = Math.abs(blockSpeed);
                }

                // 使用物理引擎设置位置
                game.scene.scenes[0].matter.body.setPosition(currentBlock, newX, currentBlockY);

                // 更新currentBlock对象的x和y属性
                currentBlock.x = newX;
                currentBlock.y = currentBlockY;

                // 同步图形表示的位置
                if (currentBlock.graphics) {
                    currentBlock.graphics.x = newX;
                    currentBlock.graphics.y = currentBlockY;
                }
            }

            // 同步所有方块的位置
            blocks.forEach(block => {
                if (block && block.graphics) {
                    // 获取物理对象的位置
                    const blockX = block.position ? block.position.x : block.x;
                    const blockY = block.position ? block.position.y : block.y;

                    // 更新图形对象的位置
                    block.graphics.x = blockX;
                    block.graphics.y = blockY;

                    // 更新block对象的x和y属性
                    block.x = blockX;
                    block.y = blockY;
                }
            });

            // 在战斗模式下，让方块开火
            if (battleMode && monster) {
                const currentTime = game.scene.scenes[0].time.now;
                blocks.forEach(block => {
                    if (block && block.canFire && currentTime - block.lastFireTime > 1000) {
                        fireAtMonster(block);
                        block.lastFireTime = currentTime;
                    }
                });
            }

            // 更新分数显示
            updateScore.call(this);
        }
    }



    // 创建游戏边界
    function createGameBounds() {
        // 创建简单的游戏边界
        // 左边界
        this.matter.add.rectangle(25, 667, 50, 1334, { isStatic: true });
        // 右边界
        this.matter.add.rectangle(725, 667, 50, 1334, { isStatic: true });
        // 上边界
        this.matter.add.rectangle(375, 25, 750, 50, { isStatic: true });
        // 下边界
        this.matter.add.rectangle(375, 1309, 750, 50, { isStatic: true, label: 'ground' });
    }

    // 创建地基
    function createGround() {
        // 创建地基物理对象
        const groundBody = this.matter.add.rectangle(375, GROUND_Y, GROUND_WIDTH, GROUND_HEIGHT, {
            isStatic: true,
            label: 'gameGround'
        });

        // 创建地基图形
        ground = this.add.rectangle(375, GROUND_Y, GROUND_WIDTH, GROUND_HEIGHT, 0x8B4513);
        ground.setDepth(1);

        console.log('创建地基，位置：', 375, GROUND_Y, '大小：', GROUND_WIDTH, 'x', GROUND_HEIGHT);
    }







    // 创建UI元素
    function createUI() {
        // 左上角分数显示
        const scoreBg = this.add.graphics();
        scoreBg.fillStyle(0x3498db); // 蓝色背景
        scoreBg.lineStyle(3, 0x000000); // 黑色边框
        scoreBg.fillRoundedRect(15, 15, 150, 60, 10); // 圆角矩形
        scoreBg.strokeRoundedRect(15, 15, 150, 60, 10);

        const scoreLabel = this.add.text(90, 35, '分数', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.scoreText = this.add.text(90, 60, '0', {
            fontSize: '24px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 中间上方游戏状态文字
        this.statusText = this.add.text(375, 50, '点击屏幕下落方块', {
            fontSize: '20px',
            fill: '#ffff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 开始战斗按钮（移到屏幕最下方）
        const buttonBg = this.add.graphics();
        buttonBg.fillStyle(0xff4444); // 红色背景
        buttonBg.lineStyle(3, 0x000000); // 黑色边框
        buttonBg.fillRoundedRect(300, 1250, 150, 50, 10);
        buttonBg.strokeRoundedRect(300, 1250, 150, 50, 10);

        startBattleButton = this.add.text(375, 1275, '开始战斗', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 设置按钮交互
        startBattleButton.setInteractive();
        startBattleButton.on('pointerdown', function() {
            if (!battleMode) {
                startBattle();
            }
        });

        // 初始更新分数
        updateScore.call(this);
    }

    // 设置输入控制
    function setupInput() {
        // 设置点击事件，用于下落方块
        this.input.on('pointerdown', function(pointer) {
            // 检查是否点击在按钮区域（避免按钮区域触发方块下落）
            if (pointer.y > 1240) {
                return; // 点击在按钮区域，不处理
            }

            if (gameStarted && !gameOver && currentBlock && currentBlock.active) {
                dropBlock();
            }
        }, this);
    }
    
    // 创建新方块
    function createNewBlock() {
        const scene = game.scene.scenes[0];

        // 计算新方块的Y位置
        let blockY = 100; // 固定在屏幕上方

        // 创建物理对象
        const blockBody = scene.matter.add.rectangle(375, blockY, BLOCK_SIZE, BLOCK_SIZE, {
            isStatic: true, // 初始设置为静态，不会下落
            render: {
                fillStyle: '#FF6B6B' // 红色方块
            }
        });

        // 创建图形表示
        const blockGraphics = scene.add.rectangle(375, blockY, BLOCK_SIZE, BLOCK_SIZE, 0xFF6B6B);
        blockGraphics.setDepth(2);

        // 将图形与物理对象关联
        blockGraphics.body = blockBody;
        blockBody.graphics = blockGraphics;

        // 保存原始位置
        blockBody.originalX = 375;

        // 设置为当前方块
        currentBlock = blockBody;

        // 重置方块速度
        blockSpeed = Math.abs(blockSpeed);

        // 确保方块可以左右移动
        currentBlock.active = true;

        console.log('创建新方块，位置：', blockY);
    }
    
    // 下落方块
    function dropBlock() {
        if (!currentBlock || !currentBlock.active || isDropping) return;

        console.log('开始下落方块');

        // 设置方块正在下落
        isDropping = true;

        // 停止左右移动
        currentBlock.active = false;

        // 将方块设置为动态，使其受重力影响下落
        game.scene.scenes[0].matter.body.setStatic(currentBlock, false);

        // 给方块一个初始的下落速度
        game.scene.scenes[0].matter.body.setVelocity(currentBlock, 0, 5);

        // 添加碰撞检测
        const collisionHandler = (event, bodyA, bodyB) => {
            // 检查是否碰撞到地基或其他方块
            if ((bodyA === currentBlock && (blocks.some(b => b && b === bodyB) || bodyB.label === 'gameGround' || bodyB.label === 'ground')) ||
                (bodyB === currentBlock && (blocks.some(b => b && b === bodyA) || bodyA.label === 'gameGround' || bodyA.label === 'ground'))) {

                console.log('方块碰撞检测触发');

                // 停止方块移动
                game.scene.scenes[0].matter.body.setStatic(currentBlock, true);
                game.scene.scenes[0].matter.body.setVelocity(currentBlock, 0, 0);

                // 设置方块可以开火
                currentBlock.canFire = true;
                currentBlock.lastFireTime = 0;

                // 添加到方块数组
                blocks.push(currentBlock);

                // 增加分数
                score += 1;

                // 重置下落状态
                isDropping = false;

                // 创建新方块
                createNewBlock();

                // 移除碰撞监听器，避免重复触发
                game.scene.scenes[0].matter.world.off('collisionstart', collisionHandler);
            }
        };

        // 添加碰撞监听器
        game.scene.scenes[0].matter.world.on('collisionstart', collisionHandler);
    }
    
    // 显示完美链接文本
    function showPerfectText(x, y) {
        const scene = game.scene.scenes[0];

        const perfectText = scene.add.text(x, y - 50, '完美!', {
            fontSize: '24px',
            fill: '#ffff00',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        scene.tweens.add({
            targets: perfectText,
            y: y - 100,
            alpha: 0,
            duration: 1000,
            onComplete: () => perfectText.destroy()
        });
    }
    

    
    // 开始战斗
    function startBattle() {
        battleMode = true;

        const scene = game.scene.scenes[0];

        // 隐藏开始战斗按钮
        startBattleButton.setVisible(false);

        // 创建怪物
        createMonster();

        // 更新状态文字
        scene.statusText.setText('战斗开始！方块开火攻击怪物');
        scene.statusText.setStyle({ fill: '#ff0000' });

        console.log('战斗模式开始');
    }

    // 创建怪物
    function createMonster() {
        const scene = game.scene.scenes[0];

        // 创建怪物图形（紫色矩形）
        monster = scene.add.rectangle(650, 600, 80, 120, 0x800080);
        monster.setDepth(5);
        monster.isMonster = true;

        // 添加血条背景
        const healthBarBg = scene.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(monster.x - 40, monster.y - 80, 80, 10);
        healthBarBg.setDepth(10);

        // 添加血条
        const healthBar = scene.add.graphics();
        healthBar.fillStyle(0xff0000);
        healthBar.fillRect(monster.x - 40, monster.y - 80, 80, 10);
        healthBar.setDepth(11);

        monster.healthBarBg = healthBarBg;
        monster.healthBar = healthBar;
        monster.maxHealth = 50;
        monster.health = 50;

        console.log('创建怪物，位置：', 650, 600);
    }

    // 方块向怪物开火
    function fireAtMonster(block) {
        if (!monster || monster.health <= 0) return;

        const scene = game.scene.scenes[0];

        // 创建子弹
        const bullet = scene.add.circle(block.x + BLOCK_SIZE/2, block.y, 5, 0xFFFF00);
        bullet.setDepth(3);

        // 计算子弹飞向怪物的方向
        const angle = Phaser.Math.Angle.Between(bullet.x, bullet.y, monster.x, monster.y);
        const speed = 300;
        const velocityX = Math.cos(angle) * speed;
        const velocityY = Math.sin(angle) * speed;

        // 子弹移动动画
        scene.tweens.add({
            targets: bullet,
            x: monster.x,
            y: monster.y,
            duration: 500,
            onComplete: () => {
                // 子弹击中怪物
                hitMonster(10); // 造成10点伤害
                bullet.destroy();
            }
        });

        console.log('方块开火');
    }

    // 击中怪物
    function hitMonster(damage) {
        if (!monster || monster.health <= 0) return;

        const scene = game.scene.scenes[0];

        monster.health -= damage;

        // 更新血条
        const healthPercent = monster.health / monster.maxHealth;
        monster.healthBar.clear();
        monster.healthBar.fillStyle(0xff0000);
        monster.healthBar.fillRect(monster.x - 40, monster.y - 80, 80 * healthPercent, 10);

        // 显示伤害数字
        const damageText = scene.add.text(monster.x, monster.y - 50, `-${damage}`, {
            fontSize: '16px',
            fill: '#ffff00',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        scene.tweens.add({
            targets: damageText,
            y: monster.y - 80,
            alpha: 0,
            duration: 1000,
            onComplete: () => damageText.destroy()
        });

        // 检查怪物是否死亡
        if (monster.health <= 0) {
            // 怪物死亡
            scene.statusText.setText('怪物被击败！胜利！');
            scene.statusText.setStyle({ fill: '#00ff00' });

            // 销毁怪物
            monster.healthBar.destroy();
            monster.healthBarBg.destroy();
            monster.destroy();
            monster = null;

            battleMode = false;
            console.log('怪物被击败');
        }
    }



    // 处理碰撞
    function handleCollision(event) {
        // 简化的碰撞处理，移除台球相关逻辑
        // 可以在这里添加其他碰撞逻辑
    }
























    
    // 更新分数显示
    function updateScore() {
        if (this.scoreText) {
            this.scoreText.setText(score.toString());
        }
    }
    
    // 游戏结束
    function endGame(isVictory) {
        gameOver = true;
        
        // 创建黑色半透明背景
        const overlay = this.add.graphics();
        overlay.fillStyle(0x000000, 0.7);
        overlay.fillRect(0, 0, 750, 1334);
        overlay.setDepth(100);
        
        // 创建结果面板
        const panelWidth = 400;
        const panelHeight = 300;
        const panelX = 375;
        const panelY = 667;
        
        const panel = this.add.graphics();
        panel.fillStyle(0xffffff, 1);
        panel.fillRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.lineStyle(4, isVictory ? 0x00ff00 : 0xff0000, 1);
        panel.strokeRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.setDepth(101);
        
        // 大emoji表情
        const emoji = isVictory ? '😊' : '😢';
        const emojiText = this.add.text(panelX, panelY - 80, emoji, {
            fontSize: '80px',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);
        
        // 成功/失败文字
        const resultText = isVictory ? '胜利！' : '游戏结束！';
        const resultColor = isVictory ? '#00ff00' : '#ff0000';
        const textResult = this.add.text(panelX, panelY - 10, resultText, {
            fontSize: '32px',
            fill: resultColor,
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);
        
        // 分数显示
        const scoreText = this.add.text(panelX, panelY + 30, `最终分数: ${score}`, {
            fontSize: '24px',
            fill: '#000000',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);
        
        // 重新开始按钮
        const button = this.add.graphics();
        button.fillStyle(0x3498db, 1);
        button.fillRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
        button.lineStyle(2, 0x000000, 1);
        button.strokeRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
        button.setDepth(102);
        button.setInteractive(new Phaser.Geom.Rectangle(panelX - 80, panelY + 70, 160, 50), Phaser.Geom.Rectangle.Contains);
        
        const buttonTextObj = this.add.text(panelX, panelY + 95, '重新开始', {
            fontSize: '24px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(103);
        
        // 按钮点击事件
        button.on('pointerdown', () => {
            // 重新开始游戏
            this.scene.restart();
        });
        
        // 按钮悬停效果
        button.on('pointerover', () => {
            button.clear();
            button.fillStyle(0x2980b9, 1);
            button.fillRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
        });
        
        button.on('pointerout', () => {
            button.clear();
            button.fillStyle(0x3498db, 1);
            button.fillRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 70, 160, 50, 10);
        });
        
        // 更新状态文字
        if (this.statusText) {
            this.statusText.setText(isVictory ? '怪物被击败！' : '楼层堆叠过高！');
            this.statusText.setStyle({ fill: isVictory ? '#00ff00' : '#ff0000', fontSize: '24px' });
        }
        
        console.log(`游戏结束: ${isVictory ? '胜利' : '失败'}`);
    }









    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
