<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景图</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        physics: {
            default: 'arcade',
            arcade: {
                gravity: { y: 0 },
                debug: false, // 关闭物理引擎调试模式，提高性能
                overlapBias: 8, // 设置重叠偏移，减少物体重叠的可能性
                fps: 60, // 提高物理引擎的帧率
                timeScale: 1 // 正常时间流速
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 初始化游戏
    game = new Phaser.Game(config);

    // 预加载函数
    function preload() {
        // 加载大背景图片
        this.load.image('gameBackground', 'images/rpg/background1-1.jpg');
    }

    // 游戏变量
    let score = 0;
    let scoreText;
    let coinCountText; // 金币数量显示文本
    let collectedCoins = [];
    let coins = []; // 金币数组，全局变量
    
    // 创建游戏场景
    function create() {
        // 添加大背景图片
        const background = this.add.image(375, 667, 'gameBackground');
        background.setDisplaySize(750, 1334); // 设置为游戏窗口大小
        background.setDepth(-10); // 设置为最底层
        
        // 创建积分显示
        scoreText = this.add.text(20, 20, '积分: 0', { fontSize: '32px', fill: '#ffffff' });
        scoreText.setDepth(10); // 确保积分显示在最上层
        
        // 创建金币数量显示
        coinCountText = this.add.text(20, 60, '金币: 0', { fontSize: '32px', fill: '#ffff00' });
        coinCountText.setDepth(10); // 确保金币数量显示在最上层
        
        // 计算画布下半部分的起始位置（画布高度的一半）
        const halfHeight = 1334 / 3;
        
        // 创建4个墙壁（在画布下半部分）
        // 左墙（加厚3倍并向右移动30px，长度缩短一半）
        const leftWall = this.add.rectangle(80, 1000, 60,300, 0x00ff00);
        this.physics.add.existing(leftWall, true);
        // 静态物体默认不可移动，无需额外设置
        
        // 右墙（加厚3倍并向左移动30px，长度缩短一半）
        const rightWall = this.add.rectangle(680, 1000, 60, 300, 0x00ff00);
        this.physics.add.existing(rightWall, true);
        // 静态物体默认不可移动，无需额外设置
        
        // 下墙（底部）（加厚3倍并向上移动30px）
        const bottomWall = this.add.rectangle(375, 1000, 750, 60, 0x00ff00);
        this.physics.add.existing(bottomWall, false); // 改为动态物体，使其能够推动金币
        // 设置底部墙壁的物理属性
        bottomWall.body.setImmovable(false); // 允许墙壁被推动
        bottomWall.body.setMass(10.0); // 增加墙壁质量，使其更有力地推动金币
        bottomWall.body.setVelocity(0, 0); // 初始速度为零，由动画控制移动
        
        // 让底部墙壁上下来回移动50px
        let wallDirection = 1; // 1表示向下移动，-1表示向上移动
        this.time.addEvent({
            delay: 50, // 每50毫秒更新一次
            callback: function() {
                // 根据方向设置墙壁的速度
                bottomWall.body.setVelocity(0, wallDirection * 50);
                
                // 检查墙壁位置，如果到达边界则改变方向
                if (bottomWall.y >= 1150) {
                    wallDirection = -1; // 到达最低点，改为向上移动
                } else if (bottomWall.y <= 1000) {
                    wallDirection = 1; // 到达最高点，改为向下移动
                }
            },
            callbackScope: this,
            loop: true // 循环执行
        });
        
       
        
        // 创建一个测试球，用于测试物理引擎和墙壁碰撞
        const testBall = this.add.circle(375, halfHeight - 100, 25, 0xffff00);
        this.physics.add.existing(testBall);
        testBall.body.setVelocity(0, 50); // 增加测试球的初始速度，使其能更有效地推动金币
        testBall.body.setBounce(0.6); // 降低弹性系数，减少穿墙可能性
        // 取消测试球的重力
        testBall.body.setGravity(0, 0);
        // 大幅减少测试球的摩擦力，使其能更有效地推动金币
        testBall.body.setDrag(5, 5);
        // 增加测试球的质量，使其能更有力地推动金币
        testBall.body.setMass(1.0);
        // 设置测试球的碰撞边界，防止穿墙
        testBall.body.setCollideWorldBounds(true);
        testBall.body.setCircle(25); // 确保物理碰撞体与视觉圆形匹配
        
        // 金币数组已在全局变量中定义
        
        // 添加设置金币碰撞的函数
        this.setupCoinCollisions = function() {
            for (let i = 0; i < coins.length; i++) {
                for (let j = i + 1; j < coins.length; j++) {
                    this.physics.add.collider(coins[i], coins[j]);
                }
            }
        }
        
        // 创建金币的函数
        function createCoin() {
            // 检查底部墙壁是否处于最低位置（y=1150）
            if (bottomWall.y >= 1140 && bottomWall.y <= 1160) {
                // 随机水平位置（在左右墙之间）
                const x = Phaser.Math.Between(100, 650);
                // 在底部墙壁处于最低位置时，金币创建在1050的位置
                const y = 1050;
                // 金币半径
                const radius = 25;
            
            // 创建金币（使用黄色圆形表示）
                const coin = this.add.circle(x, y, radius, 0xffff00);
                this.physics.add.existing(coin);
                
                // 设置随机速度（减小速度范围，使金币不会移动得太远）
                coin.body.setVelocity(Phaser.Math.Between(-30, 30), Phaser.Math.Between(-50, -10));
                coin.body.setBounce(0.3); // 进一步降低弹性系数，使金币更容易被墙壁推动
                // 取消金币的重力
                coin.body.setGravity(0, 0);
                // 减少金币的摩擦力，使金币更容易被推动
                coin.body.setDrag(2, 2);
                // 增加金币的质量，使其更容易被移动的墙壁推动
                coin.body.setMass(1.0);
                // 设置金币的碰撞边界，防止穿墙
                coin.body.setCollideWorldBounds(true);
                coin.body.setCircle(25); // 确保物理碰撞体与视觉圆形匹配
                
                // 设置金币与墙壁的碰撞检测
                this.physics.add.collider(coin, leftWall);
                this.physics.add.collider(coin, rightWall);
                this.physics.add.collider(coin, bottomWall);
                
                // 设置金币与测试球的碰撞检测
                this.physics.add.collider(coin, testBall);
                
                // 设置金币与已有金币的碰撞检测
                coins.forEach(existingCoin => {
                    this.physics.add.collider(coin, existingCoin);
                });
                
                coins.push(coin);
                
                // 重新设置所有金币之间的碰撞检测，确保新金币与所有已有金币都能正确碰撞
                this.setupCoinCollisions();
                
                // 限制金币数量，最多保留20个金币
                if (coins.length > 20) {
                    const oldestCoin = coins.shift();
                    oldestCoin.destroy();
                }
            }
        }
        
        // 立即创建第一个金币
        createCoin.call(this);
        
        // 每3秒创建一个新金币
        this.time.addEvent({
            delay: 3000, // 3秒
            callback: createCoin,
            callbackScope: this,
            loop: true // 循环执行
        });
        
        // 添加点击事件监听器，点击时在点击位置生成金币
        this.input.on('pointerdown', function(pointer) {
            createCoinAtPosition.call(this, pointer.x, pointer.y);
        }, this);
        
        // 在指定位置创建金币的函数
        function createCoinAtPosition(x, y) {
            // 金币半径
            const radius = 25;
            
            // 创建金币（使用黄色圆形表示）
            const coin = this.add.circle(x, y, radius, 0xffff00);
            this.physics.add.existing(coin);
            
            // 设置随机速度（减小速度范围，使金币不会移动得太远）
            coin.body.setVelocity(Phaser.Math.Between(-30, 30), Phaser.Math.Between(-50, -10));
            coin.body.setBounce(0.3); // 进一步降低弹性系数，使金币更容易被墙壁推动
            // 取消金币的重力
            coin.body.setGravity(0, 0);
            // 减少金币的摩擦力，使金币更容易被推动
            coin.body.setDrag(2, 2);
            // 增加金币的质量，使其更容易被移动的墙壁推动
            coin.body.setMass(1.0);
            // 设置金币的碰撞边界，防止穿墙
            coin.body.setCollideWorldBounds(true);
            coin.body.setCircle(25); // 确保物理碰撞体与视觉圆形匹配
            
            // 设置金币与墙壁的碰撞检测
            this.physics.add.collider(coin, leftWall);
            this.physics.add.collider(coin, rightWall);
            this.physics.add.collider(coin, bottomWall);
            
            // 设置金币与测试球的碰撞检测
            this.physics.add.collider(coin, testBall);
            
            // 设置金币与已有金币的碰撞检测
            coins.forEach(existingCoin => {
                this.physics.add.collider(coin, existingCoin);
            });
            
            coins.push(coin);
            
            // 重新设置所有金币之间的碰撞检测，确保新金币与所有已有金币都能正确碰撞
            this.setupCoinCollisions();
            
            // 限制金币数量，最多保留20个金币
            if (coins.length > 20) {
                const oldestCoin = coins.shift();
                oldestCoin.destroy();
            }
        }
        
        // 创建收集区域（右上角）
        const collectionArea = this.add.rectangle(650, 100, 150, 150, 0x444444, 0.3);
        collectionArea.setStrokeStyle(2, 0xffffff); // 添加白色边框
        collectionArea.setDepth(5); // 确保在背景之上但在金币之下
        
        // 设置测试球与墙壁的碰撞检测
        this.physics.add.collider(testBall, leftWall);
        this.physics.add.collider(testBall, rightWall);
        this.physics.add.collider(testBall, bottomWall);
        
        // 设置测试球与所有金币的碰撞检测
        for (let i = 0; i < coins.length; i++) {
            this.physics.add.collider(testBall, coins[i]);
        }
        
        // 设置金币之间的碰撞检测
        // 确保所有金币之间都有碰撞检测
        this.setupCoinCollisions();
    }
    
    // 更新函数，每帧调用
    function update() {
        // 检查每个金币的位置
        for (let i = coins.length - 1; i >= 0; i--) {
            const coin = coins[i];
            
            // 如果金币到达右上角区域（x坐标大于600，y坐标小于200），直接销毁
            if (coin.x > 600 && coin.y < 200) {
                // 增加积分
                score += 10;
                scoreText.setText('积分: ' + score);
                
                // 将金币从活动数组中移除
                coins.splice(i, 1);
                collectedCoins.push(coin);
                
                // 更新金币数量显示
                coinCountText.setText('金币: ' + collectedCoins.length);
                
                // 直接销毁金币
                coin.destroy();
            }
        }
    }
</script>
</body>
</html>